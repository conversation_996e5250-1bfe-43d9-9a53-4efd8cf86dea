use std::fmt::Debug;
use std::fs::File;

use anyhow::Result;
use chrono::DateTime;
use chrono::Utc;
use serde::Deserialize;
use serde::Serialize;

#[derive(Debug, Serialize, Deserialize)]
struct Human {
    name: String,
    age: u8,
}

// Claude API data structures
#[derive(Debug, Serialize, Deserialize)]
struct ClaudeMessage {
    role: String,
    content: String,
}

#[derive(Debug, Serialize)]
struct ClaudeRequest {
    model: String,
    max_tokens: u32,
    messages: Vec<ClaudeMessage>,
    system: Option<String>,
}

#[derive(Debug, Deserialize)]
struct ClaudeContent {
    #[serde(rename = "type")]
    content_type: String,
    text: String,
}

#[derive(Debug, Deserialize)]
struct ClaudeResponse {
    content: Vec<ClaudeContent>,
}

trait Agent: Debug {
    fn name(&self) -> &str;
    fn system_prompt(&self) -> &str;
    fn should_respond(&self, messages: &[Message]) -> bool;
    fn respond(&self, messages: &[Message], api_key: &str) -> Result<String>;
}

#[derive(Debug)]
struct GeneralistAgent {
    name: String,
}

impl Agent for GeneralistAgent {
    fn name(&self) -> &str {
        &self.name
    }
    fn system_prompt(&self) -> &str {
        "You're a generalist agent and should help the human with their questions and requests."
    }
    fn should_respond(&self, _messages: &[Message]) -> bool {
        true
    }
    fn respond(&self, messages: &[Message], api_key: &str) -> Result<String> {
        // For this prototype, we'll use a blocking approach with reqwest::blocking
        self.respond_sync(messages, api_key)
    }
}

impl GeneralistAgent {
    fn respond_sync(&self, messages: &[Message], api_key: &str) -> Result<String> {
        let client = reqwest::blocking::Client::new();

        // Convert internal messages to Claude API format
        let claude_messages: Vec<ClaudeMessage> = messages
            .iter()
            .filter_map(|msg| match &msg.author {
                MessageAuthor::Human => Some(ClaudeMessage {
                    role: "user".to_string(),
                    content: msg.text.clone(),
                }),
                MessageAuthor::Agent(_) => Some(ClaudeMessage {
                    role: "assistant".to_string(),
                    content: msg.text.clone(),
                }),
                MessageAuthor::System => None, // System messages are handled separately
            })
            .collect();

        let request = ClaudeRequest {
            model: "claude-3-7-sonnet-20250219".to_string(),
            max_tokens: 1024,
            messages: claude_messages,
            system: Some(self.system_prompt().to_string()),
        };

        let response = client
            .post("https://api.anthropic.com/v1/messages")
            .header("x-api-key", api_key)
            .header("anthropic-version", "2023-06-01")
            .header("content-type", "application/json")
            .json(&request)
            .send()?;

        let claude_response: ClaudeResponse = response.json()?;

        // Extract text from the first content block
        if let Some(content) = claude_response.content.first() {
            Ok(content.text.clone())
        } else {
            Ok("No response from Claude".to_string())
        }
    }
}

#[derive(Debug, Clone)]
enum MessageAuthor {
    System,
    Human,
    Agent(String), // Store agent name instead of reference
}

#[derive(Debug)]
struct Message {
    text: String,
    author: MessageAuthor,
    timestamp: DateTime<Utc>,
}

#[derive(Debug)]
struct Room {
    agents: Vec<Box<dyn Agent>>,
    human: Human,
    messages: Vec<Message>,
}

impl Room {
    fn send_message(&mut self, message: Message, api_key: &str) -> Result<()> {
        self.messages.push(message);

        // Check with all agents, in order, which would respond to the new message
        for agent in &self.agents {
            if agent.should_respond(&self.messages) {
                // Get the response from the agent
                match agent.respond(&self.messages, api_key) {
                    Ok(response_text) => {
                        // Add the agent's response as a new message
                        let agent_message = Message {
                            text: response_text,
                            author: MessageAuthor::Agent(agent.name().to_string()),
                            timestamp: Utc::now(),
                        };
                        self.messages.push(agent_message);
                        break; // Only let the first responding agent reply
                    }
                    Err(e) => {
                        eprintln!("Error getting response from agent {}: {}", agent.name(), e);
                    }
                }
            }
        }
        Ok(())
    }
}

fn main() -> Result<()> {
    let anthropic_api_key = std::env::var("ANTHROPIC_API_KEY")?;

    // load Human data from human.yaml at runtime
    let human: Human = serde_yaml::from_reader(File::open("human.yaml")?)?;

    println!("{:?}", human);

    // create agents
    let agents: Vec<Box<dyn Agent>> = vec![Box::new(GeneralistAgent {
        name: "Generalist".to_string(),
    })];

    println!("{:?}", agents);

    let mut room = Room {
        agents,
        human,
        messages: Vec::new(),
    };

    room.send_message(
        Message {
            text: "Hello, Claude! How are you?".to_string(),
            author: MessageAuthor::Human,
            timestamp: Utc::now(),
        },
        &anthropic_api_key,
    )?;

    println!("{:#?}", room);

    Ok(())
}
