use std::fmt::Debug;
use std::fs::File;

use anyhow::Result;
use chrono::DateTime;
use chrono::Utc;
use serde::Deserialize;
use serde::Serialize;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct Human {
    name: String,
    age: u8,
}

// Claude API data structures
#[derive(Debug, Serialize, Deserialize)]
struct ClaudeMessage {
    role: String,
    content: String,
}

#[derive(Debug, Serialize)]
struct ClaudeRequest {
    model: String,
    max_tokens: u32,
    messages: Vec<ClaudeMessage>,
    system: Option<String>,
}

#[derive(Debug, Deserialize)]
struct ClaudeContent {
    #[serde(rename = "type")]
    content_type: String,
    text: String,
}

#[derive(Debug, Deserialize)]
struct ClaudeResponse {
    content: Vec<ClaudeContent>,
}

// Backend trait for AI services
trait Backend: Debug {
    fn generate_response(&self, messages: &[Message], system_prompt: &str) -> Result<String>;
}

// Claude-specific backend implementation
#[derive(Debug)]
struct Claude<PERSON>ackend {
    api_key: String,
    model: String,
    max_tokens: u32,
}

impl ClaudeBackend {
    fn new(api_key: String) -> Self {
        Self {
            api_key,
            model: "claude-3-7-sonnet-20250219".to_string(),
            max_tokens: 1024,
        }
    }

    fn convert_messages(&self, messages: &[Message]) -> Vec<ClaudeMessage> {
        messages
            .iter()
            .filter_map(|msg| match &msg.author {
                MessageAuthor::Human => Some(ClaudeMessage {
                    role: "user".to_string(),
                    content: msg.text.clone(),
                }),
                MessageAuthor::Agent(_) => Some(ClaudeMessage {
                    role: "assistant".to_string(),
                    content: msg.text.clone(),
                }),
                MessageAuthor::System => None, // System messages are handled separately
            })
            .collect()
    }
}

impl Backend for ClaudeBackend {
    fn generate_response(&self, messages: &[Message], system_prompt: &str) -> Result<String> {
        let client = reqwest::blocking::Client::new();

        let claude_messages = self.convert_messages(messages);

        let request = ClaudeRequest {
            model: self.model.clone(),
            max_tokens: self.max_tokens,
            messages: claude_messages,
            system: Some(system_prompt.to_string()),
        };

        let response = client
            .post("https://api.anthropic.com/v1/messages")
            .header("x-api-key", &self.api_key)
            .header("anthropic-version", "2023-06-01")
            .header("content-type", "application/json")
            .json(&request)
            .send()?;

        let claude_response: ClaudeResponse = response.json()?;

        // Extract text from the first content block
        if let Some(content) = claude_response.content.first() {
            Ok(content.text.clone())
        } else {
            Ok("No response from Claude".to_string())
        }
    }
}

// Mock backend for testing/demonstration
#[derive(Debug)]
struct MockBackend {
    responses: Vec<String>,
    current_index: std::cell::RefCell<usize>,
}

impl MockBackend {
    fn new(responses: Vec<String>) -> Self {
        Self {
            responses,
            current_index: std::cell::RefCell::new(0),
        }
    }
}

impl Backend for MockBackend {
    fn generate_response(&self, messages: &[Message], _system_prompt: &str) -> Result<String> {
        let mut index = self.current_index.borrow_mut();
        let response = if *index < self.responses.len() {
            self.responses[*index].clone()
        } else {
            let last_message = messages
                .last()
                .map(|m| m.text.as_str())
                .unwrap_or("(no message)");
            format!("Mock response to: {}", last_message)
        };
        *index += 1;
        Ok(response)
    }
}

trait Agent: Debug {
    fn name(&self) -> &str;
    fn system_prompt(&self) -> &str;
    fn should_respond(&self, messages: &[Message]) -> bool;
    fn respond(&self, messages: &[Message]) -> Result<String>;
}

#[derive(Debug)]
struct GeneralistAgent {
    name: String,
    backend: Box<dyn Backend>,
}

impl GeneralistAgent {
    fn new(name: String, backend: Box<dyn Backend>) -> Self {
        Self { name, backend }
    }
}

impl Agent for GeneralistAgent {
    fn name(&self) -> &str {
        &self.name
    }

    fn system_prompt(&self) -> &str {
        "You're a generalist agent and should help the human with their questions and requests."
    }

    fn should_respond(&self, _messages: &[Message]) -> bool {
        // Business logic: GeneralistAgent always responds
        true
    }

    fn respond(&self, messages: &[Message]) -> Result<String> {
        // Delegate to backend for AI generation
        self.backend
            .generate_response(messages, self.system_prompt())
    }
}

#[derive(Debug, Clone)]
enum MessageAuthor {
    System,
    Human,
    Agent(String), // Store agent name instead of reference
}

#[derive(Debug)]
struct Message {
    text: String,
    author: MessageAuthor,
    timestamp: DateTime<Utc>,
}

#[derive(Debug)]
struct Room {
    agents: Vec<Box<dyn Agent>>,
    human: Human,
    messages: Vec<Message>,
}

impl Room {
    fn send_message(&mut self, message: Message) -> Result<()> {
        self.messages.push(message);

        // Check with all agents, in order, which would respond to the new message
        for agent in &self.agents {
            if agent.should_respond(&self.messages) {
                // Get the response from the agent
                match agent.respond(&self.messages) {
                    Ok(response_text) => {
                        // Add the agent's response as a new message
                        let agent_message = Message {
                            text: response_text,
                            author: MessageAuthor::Agent(agent.name().to_string()),
                            timestamp: Utc::now(),
                        };
                        self.messages.push(agent_message);
                        break; // Only let the first responding agent reply
                    }
                    Err(e) => {
                        eprintln!("Error getting response from agent {}: {}", agent.name(), e);
                    }
                }
            }
        }
        Ok(())
    }
}

fn main() -> Result<()> {
    // load Human data from human.yaml at runtime
    let human: Human = serde_yaml::from_reader(File::open("human.yaml")?)?;

    println!("=== Human Data ===");
    println!("{:?}", human);

    // Demonstrate with mock backend first
    println!("\n=== Testing with Mock Backend ===");
    let mock_backend = MockBackend::new(vec![
        "Hello! I'm doing great, thanks for asking!".to_string(),
        "How can I help you today?".to_string(),
    ]);

    let mock_agent = GeneralistAgent::new("MockAgent".to_string(), Box::new(mock_backend));

    let mut mock_room = Room {
        agents: vec![Box::new(mock_agent)],
        human: human.clone(),
        messages: Vec::new(),
    };

    mock_room.send_message(Message {
        text: "Hello, how are you?".to_string(),
        author: MessageAuthor::Human,
        timestamp: Utc::now(),
    })?;

    mock_room.send_message(Message {
        text: "What can you do?".to_string(),
        author: MessageAuthor::Human,
        timestamp: Utc::now(),
    })?;

    println!("Mock conversation:");
    for message in &mock_room.messages {
        println!("  {:?}: {}", message.author, message.text);
    }

    // Try with Claude backend if API key is available
    if let Ok(anthropic_api_key) = std::env::var("ANTHROPIC_API_KEY") {
        if anthropic_api_key != "test-key" {
            println!("\n=== Testing with Claude Backend ===");
            let claude_backend = ClaudeBackend::new(anthropic_api_key);

            let claude_agent = GeneralistAgent::new("Claude".to_string(), Box::new(claude_backend));

            let mut claude_room = Room {
                agents: vec![Box::new(claude_agent)],
                human,
                messages: Vec::new(),
            };

            claude_room.send_message(Message {
                text: format!("Hel
                author: MessageAuthor::Human,
                timestamp: Utc::now(),
            })?;

            println!("Claude conversation:");
            for message in &claude_room.messages {
                println!("  {:?}: {}", message.author, message.text);
            }
        } else {
            println!("\n=== Claude Backend (Test Mode) ===");
            println!("Using test API key - would make real API call with valid key");
        }
    } else {
        println!("\n=== Claude Backend ===");
        println!("ANTHROPIC_API_KEY not set - skipping Claude backend test");
    }

    Ok(())
}
