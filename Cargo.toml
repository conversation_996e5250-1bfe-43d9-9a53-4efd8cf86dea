[package]
name = "maia"
version = "0.1.0"
edition = "2024"
description = "The Multi AI Agent chat application"
authors = ["<PERSON> <<EMAIL>>"]

[dependencies]
anyhow = "1.0.98"
chrono = { version = "0.4.41", features = ["serde"] }
reqwest = { version = "0.12.15", features = ["json", "blocking"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_yaml = "0.9.34"
tokio = { version = "1.45.1", features = ["full"] }
